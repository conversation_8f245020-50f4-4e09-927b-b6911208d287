#!/usr/bin/env node

const { spawn } = require('child_process');
const TunnelManager = require('../tunnel-config');
const path = require('path');

class DevServer {
  constructor() {
    this.tunnelManager = new TunnelManager();
    this.serverProcess = null;
    this.tunnelActive = false;
  }

  async start(options = {}) {
    const {
      tunnelType = process.env.TUNNEL_TYPE || 'ngrok',
      port = parseInt(process.env.TUNNEL_PORT || process.env.PORT || '5000'),
      subdomain = process.env.TUNNEL_SUBDOMAIN || 'eventapp-backend',
      region = process.env.TUNNEL_REGION || 'us'
    } = options;

    console.log('🚀 Starting Event App Backend with Tunnel...');
    console.log(`📡 Tunnel Type: ${tunnelType}`);
    console.log(`🔌 Port: ${port}`);

    try {
      // Start the backend server
      await this.startServer(port);
      
      // Wait a moment for server to fully start
      await this.sleep(2000);
      
      // Start the tunnel
      await this.startTunnel(tunnelType, port, { subdomain, region });
      
      console.log('\n✅ Development environment ready!');
      console.log('📱 Your backend is now accessible from anywhere');
      this.tunnelManager.displayStatus();
      
      // Keep the process alive
      console.log('\n⏳ Press Ctrl+C to stop the server and tunnel');
      
    } catch (error) {
      console.error('❌ Failed to start development environment:', error.message);
      await this.cleanup();
      process.exit(1);
    }
  }

  startServer(port) {
    return new Promise((resolve, reject) => {
      console.log('🔄 Starting backend server...');
      
      const serverScript = path.join(__dirname, '..', 'server.js');
      this.serverProcess = spawn('node', [serverScript], {
        stdio: ['ignore', 'pipe', 'pipe'],
        env: { ...process.env, PORT: port.toString() }
      });

      let serverStarted = false;

      this.serverProcess.stdout.on('data', (data) => {
        const output = data.toString();
        console.log(output);
        
        if (output.includes('Server running on port') && !serverStarted) {
          serverStarted = true;
          resolve();
        }
      });

      this.serverProcess.stderr.on('data', (data) => {
        console.error('Server error:', data.toString());
      });

      this.serverProcess.on('close', (code) => {
        console.log(`Server process exited with code ${code}`);
        if (!serverStarted) {
          reject(new Error(`Server failed to start (exit code: ${code})`));
        }
      });

      this.serverProcess.on('error', (error) => {
        reject(new Error(`Failed to start server: ${error.message}`));
      });

      // Timeout after 30 seconds
      setTimeout(() => {
        if (!serverStarted) {
          this.serverProcess.kill();
          reject(new Error('Server startup timeout'));
        }
      }, 30000);
    });
  }

  async startTunnel(type, port, options) {
    try {
      switch (type.toLowerCase()) {
        case 'ngrok':
          await this.tunnelManager.startNgrok(port, { region: options.region });
          break;
        case 'cloudflare':
          await this.tunnelManager.startCloudflare(port);
          break;
        case 'localtunnel':
          await this.tunnelManager.startLocalTunnel(port, options.subdomain);
          break;
        default:
          throw new Error(`Unknown tunnel type: ${type}`);
      }
      this.tunnelActive = true;
    } catch (error) {
      throw new Error(`Failed to start ${type} tunnel: ${error.message}`);
    }
  }

  async cleanup() {
    console.log('\n🛑 Cleaning up...');
    
    if (this.tunnelActive) {
      await this.tunnelManager.stopAllTunnels();
    }
    
    if (this.serverProcess) {
      this.serverProcess.kill();
      console.log('✅ Server stopped');
    }
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Handle graceful shutdown
const devServer = new DevServer();

process.on('SIGINT', async () => {
  await devServer.cleanup();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await devServer.cleanup();
  process.exit(0);
});

// Parse command line arguments
const args = process.argv.slice(2);
const options = {};

for (let i = 0; i < args.length; i += 2) {
  const key = args[i]?.replace('--', '');
  const value = args[i + 1];
  if (key && value) {
    options[key] = value;
  }
}

// Start the development server
devServer.start(options).catch(error => {
  console.error('❌ Startup failed:', error.message);
  process.exit(1);
});
