{"name": "eventapp-backend", "version": "1.0.0", "description": "Node.js backend server for event app with Firebase integration", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "dev:tunnel": "node scripts/dev-with-tunnel.js", "dev:ngrok": "node scripts/dev-with-tunnel.js --tunnelType ngrok", "dev:cloudflare": "node scripts/dev-with-tunnel.js --tunnelType cloudflare", "dev:localtunnel": "node scripts/dev-with-tunnel.js --tunnelType localtunnel", "tunnel:start": "node scripts/tunnel.js start", "tunnel:stop": "node scripts/tunnel.js stop", "tunnel:status": "node scripts/tunnel.js status", "tunnel:ngrok": "node scripts/tunnel.js start --type ngrok", "tunnel:cloudflare": "node scripts/tunnel.js start --type cloudflare", "tunnel:localtunnel": "node scripts/tunnel.js start --type localtunnel", "tunnel:check": "node scripts/check-tunnel-deps.js", "test": "jest", "test-firebase": "node scripts/testFirebase.js", "init-db": "node scripts/initDatabase.js", "seed-data": "node scripts/seedData.js", "validate-schema": "node -e \"const SchemaValidator = require('./utils/schemaValidator'); console.log('Schema validation utility loaded successfully');\""}, "keywords": ["nodejs", "express", "firebase", "event-management", "rest-api"], "author": "Event App Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "firebase-admin": "^11.11.1", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "ngrok": "^5.0.0-beta.2", "localtunnel": "^2.0.2", "commander": "^11.1.0"}, "engines": {"node": ">=16.0.0"}}