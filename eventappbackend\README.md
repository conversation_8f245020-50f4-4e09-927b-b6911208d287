# Event App Backend

A Node.js backend server for the Event Management App with Firebase integration and REST APIs.

## Features

- 🚀 Express.js REST API server
- 🔥 Firebase Firestore integration
- 📊 Event data management
- 🛍️ Service catalog management
- 📅 Booking system
- 🔒 Input validation and sanitization
- 🛡️ Security middleware (Helmet, CORS, Rate limiting)
- 📝 Comprehensive logging
- 🏥 Health check endpoints
- 📱 Mobile-friendly API responses

## API Endpoints

### Health Check
- `GET /api/health` - Basic health check
- `GET /api/health/system` - Detailed system information

### Events
- `GET /api/events` - Get all event types
- `GET /api/events/:type` - Get specific event type (Birthday, Corporate, HouseWarming, Conference)
- `GET /api/events/:type/questions` - Get event-specific questions
- `POST /api/events/seed` - Seed event data to Firebase

### Services
- `GET /api/services` - Get all services
- `GET /api/services/:id` - Get service by ID
- `GET /api/services/category/:category` - Get services by category
- `GET /api/services/search?q=term` - Search services
- `POST /api/services/seed` - Seed service data to Firebase

### Bookings
- `POST /api/bookings` - Create new booking
- `GET /api/bookings` - Get all bookings (with filters)
- `GET /api/bookings/:id` - Get booking by ID
- `PUT /api/bookings/:id` - Update booking
- `PATCH /api/bookings/:id/status` - Update booking status
- `DELETE /api/bookings/:id` - Delete booking

## Setup Instructions

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Firebase project with Firestore enabled

### Installation

1. **Clone and navigate to the backend directory:**
   ```bash
   cd eventappbackend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Set up environment variables:**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` file with your Firebase configuration:
   ```env
   PORT=5000
   NODE_ENV=development
   FIREBASE_PROJECT_ID=your-firebase-project-id
   FIREBASE_DATABASE_URL=https://your-project-id-default-rtdb.firebaseio.com/
   FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account",...}
   ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8081
   ```

4. **Firebase Setup Options:**

   **Option A: Service Account Key (Recommended for Production)**
   - Go to Firebase Console → Project Settings → Service Accounts
   - Generate new private key
   - Copy the JSON content to `FIREBASE_SERVICE_ACCOUNT_KEY` in .env

   **Option B: Application Default Credentials (Development)**
   - Install Google Cloud SDK
   - Run: `gcloud auth application-default login`
   - Set `GOOGLE_APPLICATION_CREDENTIALS` path in .env

   **Option C: Service Account File**
   - Download service account key file
   - Place it in `config/serviceAccountKey.json`
   - Set `GOOGLE_APPLICATION_CREDENTIALS=./config/serviceAccountKey.json` in .env

### Running the Server

1. **Development mode (with auto-restart):**
   ```bash
   npm run dev
   ```

2. **Production mode:**
   ```bash
   npm start
   ```

3. **Run tests:**
   ```bash
   npm test
   ```

The server will start on `http://localhost:5000` (or the port specified in .env).

### Initial Data Setup

The server automatically seeds data from the static JSON files when no data is found in Firebase:

1. **Database initialization (Recommended):**
   ```bash
   npm run init-db
   ```

2. **Automatic seeding:** Data is seeded when endpoints are first accessed

3. **Manual seeding:** Use the seed endpoints:
   ```bash
   POST http://localhost:5000/api/events/seed
   POST http://localhost:5000/api/services/seed
   ```

### Database Schema

The application uses a well-defined schema for Firestore collections:

- **Events** - Event type definitions with questions and metadata
- **Services** - Service catalog with packages and pricing
- **Bookings** - Customer bookings with status tracking
- **Users** - User profiles and authentication data
- **Vendors** - Service provider information
- **Reviews** - Customer reviews and ratings

See [Schema Documentation](docs/SCHEMA.md) for detailed schema definitions.

#### Schema Validation

All data is validated against predefined schemas before saving to Firestore:

```bash
# Validate schema definitions
npm run validate-schema
```

#### Security Rules

Firestore security rules are defined in `firestore.rules` and include:
- Public read access for events and services
- Authenticated access for bookings
- Role-based access control for users and vendors

## Project Structure

```
eventappbackend/
├── config/
│   └── firebase.js          # Firebase configuration
├── middleware/
│   ├── errorHandler.js      # Global error handling
│   ├── notFound.js          # 404 handler
│   └── validation.js        # Input validation rules
├── routes/
│   ├── health.js            # Health check routes
│   ├── events.js            # Event management routes
│   ├── services.js          # Service catalog routes
│   └── bookings.js          # Booking system routes
├── .env.example             # Environment variables template
├── package.json             # Dependencies and scripts
├── server.js                # Main server file
└── README.md                # This file
```

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `PORT` | Server port | No (default: 5000) |
| `NODE_ENV` | Environment mode | No (default: development) |
| `FIREBASE_PROJECT_ID` | Firebase project ID | Yes |
| `FIREBASE_DATABASE_URL` | Firebase database URL | No |
| `FIREBASE_SERVICE_ACCOUNT_KEY` | Service account JSON | Yes (or use file) |
| `GOOGLE_APPLICATION_CREDENTIALS` | Path to service account file | Alternative to above |
| `ALLOWED_ORIGINS` | CORS allowed origins | No |

## API Response Format

All API responses follow this format:

```json
{
  "success": true,
  "data": {...},
  "count": 10,
  "total": 100,
  "message": "Optional message"
}
```

Error responses:
```json
{
  "success": false,
  "error": {
    "message": "Error description",
    "details": "Additional details"
  }
}
```

## Security Features

- **Helmet.js** - Security headers
- **CORS** - Cross-origin resource sharing
- **Rate Limiting** - Prevents abuse
- **Input Validation** - Validates and sanitizes input
- **Error Handling** - Secure error responses

## Development

### Adding New Routes

1. Create route file in `routes/` directory
2. Add validation rules in `middleware/validation.js`
3. Import and use in `server.js`

### Testing

The project includes Jest for testing. Add test files with `.test.js` extension.

## Deployment

### Environment Setup
- Set `NODE_ENV=production`
- Use proper Firebase service account
- Configure CORS for production domains
- Set up proper logging

### Recommended Platforms
- **Heroku** - Easy deployment with buildpacks
- **Google Cloud Run** - Serverless container deployment
- **AWS EC2/ECS** - Full control deployment
- **Vercel** - Serverless functions

## Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new features
4. Ensure all tests pass
5. Submit pull request

## License

MIT License - see LICENSE file for details.
